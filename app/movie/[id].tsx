import { icons } from "@/constants/icons";
import { fetchMovieDetails } from "@/services/api";
import useFetch from "@/services/useFetch";
import { router, useLocalSearchParams } from "expo-router";
import React from "react";
import { Image, ScrollView, Text, TouchableOpacity, View } from "react-native";

const MovieInfo = ({
  label,
  value,
}: {
  label: string;
  value: string | number | null | undefined;
}) => {
  return (
    <View className="flex-col items-start pl-4 pr-4 justify-center  mt-5">
      <Text className="text-light-200 font-normal text-sm">{label}</Text>
      <Text className="text-white font-bold text-sm mt-2">
        {value || "N/A"}
      </Text>
    </View>
  );
};

const MovieDetails = () => {
  const { id } = useLocalSearchParams();

  const { data: movie } = useFetch(() => fetchMovieDetails(id as string));

  return (
    <View className="bg-primary flex-1">
      <ScrollView contentContainerStyle={{ paddingBottom: 80 }}>
        <View>
          <Image
            source={{
              uri: `https://image.tmdb.org/t/p/w500${movie?.backdrop_path}`,
            }}
            className="w-full h-[550px]"
          />
        </View>

        <View className="flex-col items-start justify-center mt-5 px-5">
          <Text className="text-white font-bold text-xl ">{movie?.title}</Text>
        </View>
        <View className="flex-row items-center gap-1 pl-6 mt-2">
          <Text className="text-light-200 text-sm">
            {movie?.release_date.split("-")[0]}
          </Text>
          <Text className="text-light-200 text-sm ">{movie?.runtime}m</Text>
        </View>

        <View className="flex-row items-center pl-6 bg-dark-100 px-2 py-1 rounded-md gap-x-1 mt-2 ">
          <Image source={icons.star} className="w-4 h-4" />
          <Text className="text-white font-bold text-sm">
            {Math.round(movie?.vote_average ?? 0)}/ 10
          </Text>
          <Text className="text-light-200 text-sm">
            {movie?.vote_count} &nbsp; votes{" "}
          </Text>
        </View>

        <MovieInfo label="Overview" value={movie?.overview} />
        <MovieInfo
          label="Geners"
          value={movie?.genres?.map((g) => g.name).join(" - ") || "N/A"}
        />

        <View className="flex flex-row justify-between w1/2 ">
          <MovieInfo label="Budget" value={`$${movie?.budget! / 1000000}M  `} />
          <MovieInfo
            label="revenue"
            value={`${Math.round(movie?.revenue ?? 0) / 1000000}M`}
          />
        </View>

        <MovieInfo
          label="Production Companies"
          value={
            movie?.production_companies?.map((c) => c.name).join(" - ") || "N/A"
          }
        />
      </ScrollView>

      <TouchableOpacity
        className="mx-5 absolute bottom-5 left-0 right-0 bg-accent rounded-lg py-3.5 flex flex-row justify-center items-center z-50"
        onPress={router.back}
      >
        <Image
          source={icons.arrow}
          className="size-5  mr-1 mt-o.5 rotate-180 "
          tintColor={"#fff"}
        />
        <Text className="text-white font-bold text-base">Go back</Text>
      </TouchableOpacity>
    </View>
  );
};

export default MovieDetails;
